

// Custom styles for markdown content
const markdownStyles = `
  .markdown-content {
    font-size: 15px;
    line-height: 1.6;
    color: #FFFFFF;
  }
  
  .markdown-content h1,
  .markdown-content h2,
  .markdown-content h3 {
    font-weight: 600;
    margin-top: 0.75rem;
    margin-bottom: 0.5rem;
    color: #FFFFFF;
  }
  
  .markdown-content h1 { font-size: 1.25rem; }
  .markdown-content h2 { font-size: 1.15rem; }
  .markdown-content h3 { font-size: 1rem; }
  
  .markdown-content p {
    margin-bottom: 0.5rem;
    color: #FFFFFF;
  }
  
  .markdown-content ul,
  .markdown-content ol {
    margin: 0.5rem 0 0.5rem 1.5rem;
    color: #FFFFFF;
    list-style-position: outside;
  }
  
  .markdown-content li {
    margin-bottom: 0.25rem;
  }
  
  .markdown-content code {
    background-color: #0E1117;
    padding: 0.2rem 0.4rem;
    border-radius: 0.375rem;
    font-size: 14px;
    color: #FFFFFF;
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  }
  
  .markdown-content pre {
    background-color: #0E1117;
    padding: 0.75rem;
    border-radius: 0.75rem;
    overflow-x: auto;
    margin: 0.5rem 0;
  }
  
  .markdown-content pre code {
    background-color: transparent;
    padding: 0;
    border-radius: 0;
    color: #FFFFFF;
  }
  
  .markdown-content a {
    color: #4069FF;
    text-decoration: underline;
    text-underline-offset: 2px;
    transition: all 0.2s ease;
    padding: 0 2px;
    border-radius: 3px;
  }
  
  .markdown-content a:hover {
    color: #3558D6;
    background-color: rgba(64, 105, 255, 0.1);
  }

  .markdown-content a:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(64, 105, 255, 0.3);
  }
  
  .markdown-content blockquote {
    border-left: 3px solid #4069FF;
    padding: 0.5rem 1rem;
    margin: 0.5rem 0;
    background-color: #0E1117;
    border-radius: 0.5rem;
    color: #FFFFFF;
  }
  
  .markdown-content table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin: 0.5rem 0;
    background-color: #0E1117;
    border-radius: 0.75rem;
    overflow: hidden;
  }
  
  .markdown-content th,
  .markdown-content td {
    border: 1px solid #2D3139;
    padding: 0.5rem 0.75rem;
    color: #FFFFFF;
  }
  
  .markdown-content th {
    background-color: #0E1117;
    font-weight: 600;
    text-align: left;
  }
  
  .markdown-content tr:nth-child(even) {
    background-color: #1C1F26;
  }
  
  .markdown-content img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    margin: 0.5rem 0;
  }
  
  .markdown-content hr {
    border: 0;
    border-top: 1px solid #2D3139;
    margin: 1rem 0;
  }
  
  .markdown-content strong {
    font-weight: 600;
    color: #FFFFFF;
  }
  
  .markdown-content em {
    font-style: italic;
    color: #FFFFFF;
  }
`;

export { markdownStyles };