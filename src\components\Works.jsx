import { styles } from '../styles';
import { github } from '../assets';
import { browser } from '../assets';
import { SectionWrapper } from '../hoc';
import { projects } from '../constants';
import { Tilt } from 'react-tilt';

const ProjectCard = ({ name, description, tags, image, source_code_link }) => {
  return (
    <div className="w-full sm:w-[360px]">
      <Tilt
        options={{
          max: 45,
          scale: 1,
          speed: 350
        }}
        className="bg-tertiary p-5 rounded-2xl h-full"
      > 
        <div className="relative w-full h-[230px]">
          <img 
            src={image} 
            alt={name} 
            className="w-full h-full object-cover rounded-2xl"
          />

          <div className="absolute inset-0 flex justify-end m-3 card-img_hover">
            <div 
              onClick={() => window.open(source_code_link, "_blank")} 
              className="black-gradient w-10 h-10 rounded-full flex justify-center items-center cursor-pointer"
            >
              <img 
                src={source_code_link && source_code_link.includes('github') ? github : browser} 
                alt="source code" 
                className="w-1/2 h-1/2 object-contain" 
              />
            </div>
          </div>
        </div>

        <div className="mt-5">
          <h3 className="text-white font-bold text-[24px]">{name}</h3>
          <p className="mt-2 text-secondary text-[14px]">{description}</p>
        </div>

        <div className="mt-4 flex flex-wrap gap-2">
          {tags.map((tag) => (
            <p key={tag.name} className={`text-[14px] ${tag.color}`}>
              #{tag.name}
            </p>
          ))}
        </div>
      </Tilt>
    </div>
  );
};

const Works = () => {
  return (
    <div className="w-full">
      <div>
        <p className={styles.sectionSubText}>My work</p>
        <h2 className={styles.sectionHeadText}>Projects.</h2>
      </div>

      <div className="w-full">
        <p className="mt-3 text-secondary text-[17px] max-w-3xl leading-[30px]">
          These projects exemplify my skills and experience through real-world applications of my work. 
          Each project is concisely presented with links to its code repository and live demo, 
          offering insight into my ability to tackle complex challenges, adapt to various technologies, 
          and manage projects efficiently from inception to completion.
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mt-20">
        {projects.map((project, index) => (
          <ProjectCard 
            key={`project-${index}`}
            {...project}
          />
        ))}
      </div>
    </div>
  );
};

export default SectionWrapper(Works, "projects");