import React, { useState, } from 'react';
import { MessageCircle, X,} from 'lucide-react';
import ChatWindow from '../components/ChatWindow';



const ChatIcon = ({ isOpen, onClick }) => {
  
  return (
    <div className="fixed bottom-6 right-6 z-50">
      {!isOpen && (
        <div className="absolute bottom-full mb-2 right-0 animate-bounce">
          <div className="bg-black text-white text-sm py-1 px-3 rounded-lg shadow-lg whitespace-nowrap">
            Let's chat! 👋
            <div className="absolute -bottom-1 right-6 w-2 h-2 bg-black transform rotate-45"></div>
          </div>
        </div>
      )}

      {!isOpen && (
        <>
          <span className="absolute inset-0 rounded-full bg-[#4069FF] animate-ping opacity-25"></span>
          <span className="absolute inset-0 rounded-full bg-[#4069FF] animate-pulse opacity-75"></span>
        </>
      )}

      <button
        onClick={onClick}
        className={`relative w-14 h-14 rounded-full 
          flex items-center justify-center
          shadow-lg hover:shadow-xl
          transition-all duration-300 ease-in-out
          ${isOpen ? 'md:block hidden' : 'block'}
          ${isOpen ? 'bg-red-500' : 'bg-[#1C1F26] hover:bg-[#3558D6]'}
          transform hover:scale-110 active:scale-95`}
      >
        <div className={`flex items-center justify-center w-full h-full
          transition-all duration-300
          ${!isOpen ? 'animate-bounce' : ''}`}
        >
          {isOpen ? (
            <X size={24} className="text-white" />
          ) : (
            <MessageCircle 
              size={24} 
              className="text-white transition-transform duration-300 ease-in-out"
            />
          )}
        </div>

        <div className="absolute -top-1 -right-1">
          <div className="relative">
            <div className="w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
            <div className="absolute inset-0 bg-green-500 rounded-full animate-ping opacity-75"></div>
          </div>
        </div>
      </button>
    </div>
  );
};

const InteractiveChat = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <ChatWindow isOpen={isOpen} onClose={() => setIsOpen(false)} />
      <ChatIcon isOpen={isOpen} onClick={() => setIsOpen(!isOpen)} />
    </>
  );
};

export default InteractiveChat;