{"homepage": "https://shuvo881.github.io", "name": "fontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"predeploy": "npm run build", "deploy": "gh-pages -d dist", "dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@langchain/anthropic": "^0.3.13", "@langchain/community": "^0.3.30", "@langchain/core": "^0.3.40", "@langchain/groq": "^0.1.3", "@langchain/mistralai": "^0.2.0", "@langchain/openai": "^0.4.4", "@react-three/drei": "^9.112.0", "@react-three/fiber": "^8.17.7", "@types/three": "^0.168.0", "cheerio": "^1.0.0", "dompurify": "^3.2.4", "dotenv": "^16.4.7", "framer-motion": "^11.5.4", "langchain": "^0.3.15", "lucide-react": "^0.468.0", "maath": "^0.10.8", "marked": "^15.0.6", "patch-package": "^8.0.0", "prop-types": "^15.8.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.4.0", "react-router-dom": "^6.26.2", "react-tilt": "^1.0.2", "react-toastify": "^10.0.6", "react-tsparticles": "^2.12.2", "react-vertical-timeline-component": "^3.6.0", "three": "^0.168.0", "tsparticles": "^2.12.0", "tsparticles-engine": "^2.12.0", "zod": "^3.24.2"}, "devDependencies": {"@types/react": "^18.3.7", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^8.57.1", "eslint-plugin-react": "^7.36.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.12", "gh-pages": "^6.1.1", "postcss": "^8.4.47", "tailwindcss": "^3.4.12", "terser": "^5.33.0", "vite": "^5.4.6"}}