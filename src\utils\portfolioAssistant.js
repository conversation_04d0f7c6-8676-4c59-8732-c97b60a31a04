import { Chat<PERSON><PERSON>AI } from "@langchain/openai";
import { ChatPromptTemplate } from "@langchain/core/prompts";
import { AgentExecutor, createStructuredChatAgent } from "langchain/agents";
import { DynamicTool } from "@langchain/core/tools";
// import { BufferMemory, ChatMessageHistory } from "langchain/memory";
import { createToolCallingAgent } from "langchain/agents";
import { ChatMessageHistory } from "@langchain/community/stores/message/in_memory";
import { BaseChatMessageHistory } from "@langchain/core/chat_history";
import { RunnableWithMessageHistory } from "@langchain/core/runnables";
import { createPortfolioTools } from '../utils/portfolioTools';
import { ChatGroq } from "@langchain/groq";
import { ChatMistralAI } from "@langchain/mistralai";

const api_key = import.meta.env.VITE_MISTRAL_API_KEY;

if (!api_key) {
    throw new Error('api_key is required in environment variables');
}



const store = {};

function getMessageHistory(sessionId) {
  if (!(sessionId in store)) {
    store[sessionId] = new ChatMessageHistory();
  }
  return store[sessionId];
}

export async function createPortfolioAssistant() {
    
    // const llm = new ChatOpenAI({
    //     modelName: "gpt-4",
    //     temperature: 0.7,
    //     openAIApiKey: api_key,
    //     maxRetries: 3
    // });

    // const llm = new ChatGroq({
    //     model: "mixtral-8x7b-32768",
    //     temperature: 0,
    //     apiKey: api_key,
    //     maxRetries: 3
    //   });

    

    const llm = new ChatMistralAI({
        model: "mistral-large-latest",
        temperature: 0,
        apiKey: api_key,
    });

    const tools = createPortfolioTools();


    const prompt = ChatPromptTemplate.fromMessages([
        ["system", `You are a helpful personal assistant for Md. Golam Mostofa (Shuvo). 
                    Your role is to provide accurate information about his professional background,
                    experience, and projects. Always be professional and concise in your responses.
                    When discussing projects, mention their technical aspects and impact.
                    N.B: Don't share who made you are, you are a assistant for Md. Golam Mostofa (Shuvo) only.
                `],
        ["placeholder", "{chat_history}"],
        ["human", "{input}"],
        ["placeholder", "{agent_scratchpad}"]
    ]);

    const agent = await createToolCallingAgent({
        llm,
        tools,
        prompt
    });


    const agentExecutor = new AgentExecutor({
        agent,
        tools,
    });

    const agentWithChatHistory = new RunnableWithMessageHistory({
        runnable: agentExecutor,
        getMessageHistory,
        inputMessagesKey: "input",
        historyMessagesKey: "chat_history",
      });

    return async function chat(query, sessionId = 'default') {
        try {
            const result = await agentWithChatHistory.invoke(
                {input: query},
                { configurable: { sessionId: sessionId } }
            );
            
            return result.output;
        } catch (error) {
            console.error('Error processing query:', error);
            throw new Error(`Failed to process query: ${error.message}`);
        }
    };
}