<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Redirect - GitHub Pages</title>
    <script type="text/javascript">
        (function(l) {
            // Configuration
            const config = {
                repositoryName: '/shuvo881.github.io',
                segmentCount: 1,
                defaultRedirect: '/404.html'
            };

            // Utility functions
            function cleanPath(path) {
                // Remove repository name from path if present
                if (path.startsWith(config.repositoryName)) {
                    path = path.substring(config.repositoryName.length);
                }
                return path || '/';
            }

            function encodeParams(params) {
                return params.replace(/[&?]/g, '~and~');
            }

            function buildRedirectUrl() {
                try {
                    // Process the pathname
                    const cleanedPath = cleanPath(l.pathname);
                    const pathSegments = cleanedPath.slice(1).split('/').slice(config.segmentCount);
                    
                    // Build the base redirect URL
                    const baseUrl = `${l.protocol}//${l.hostname}${l.port ? ':' + l.port : ''}${config.repositoryName}`;
                    
                    // Process search and hash parameters
                    const queryParams = l.search ? `&q=${encodeParams(l.search.slice(1))}` : '';
                    const hashFragment = l.hash || '';
                    
                    // Combine all parts
                    return `${baseUrl}/?p=/${pathSegments.join('/')}${queryParams}${hashFragment}`;
                } catch (error) {
                    console.error('Error building redirect URL:', error);
                    return `${config.repositoryName}${config.defaultRedirect}`;
                }
            }

            // Execute redirect
            try {
                const redirectUrl = buildRedirectUrl();
                window.location.replace(redirectUrl);
            } catch (error) {
                console.error('Critical redirect error:', error);
                // Fallback to home page in case of error
                window.location.replace(config.repositoryName);
            }
        }(window.location));
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            margin: 0;
            padding: 20px;
            text-align: center;
        }
        .message {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="message">
        <h1>Redirecting...</h1>
        <p>If you are not redirected automatically, please click <a href="/">here</a>.</p>
    </div>
    <noscript>
        <p>JavaScript is required to redirect to the correct page.</p>
    </noscript>
</body>
</html>