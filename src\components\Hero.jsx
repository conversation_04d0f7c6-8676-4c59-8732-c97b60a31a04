import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Particles from "react-tsparticles";
import { loadSlim } from "tsparticles-slim";
import { FaGithub, FaLinkedin, FaTwitter, FaMedium, FaEnvelope, FaArrowDown, FaCode, FaBrain, FaServer, FaRobot } from 'react-icons/fa';
import {me} from '../assets/index'
import {socialLinks} from '../constants';



// const skills = [
//   { icon: FaRobot, label: 'AI & ML', color: '#915eff' },
//   { icon: FaCode, label: 'Programming', color: '#915eff' },
//   { icon: FaBrain, label: 'Deep Learning', color: '#915eff' },
//   { icon: FaServer, label: 'Backend Dev', color: '#915eff' },
// ];

const roles = [
  'AI Specialist',
  'Machine Learning Engineer',
  'LLM Engineer',
  'Backend Developer',
  'Competitive Programmer',
];

const SocialLink = ({ href, icon: Icon, name, color }) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <motion.div 
      className="relative group"
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.95 }}
      transition={{ type: "spring", stiffness: 400, damping: 17 }}
    >
      <motion.a 
        href={href}
        target="_blank"
        rel="noreferrer"
        className="w-12 h-12 sm:w-14 sm:h-14 rounded-2xl flex justify-center items-center border-2 border-white/20 text-white/90 transition-all duration-300 cursor-pointer backdrop-blur-sm bg-white/5"
        style={{
          borderColor: isHovered ? color : 'rgba(255, 255, 255, 0.2)',
          color: isHovered ? color : 'rgba(255, 255, 255, 0.9)'
        }}
        aria-label={name}
      >
        <Icon className="w-6 h-6 sm:w-7 sm:h-7" />
        <motion.div
          className="absolute inset-0 rounded-2xl bg-white/10"
          initial={false}
          animate={{
            scale: isHovered ? 1.1 : 1,
            opacity: isHovered ? 0 : 1
          }}
          transition={{ duration: 0.3 }}
        />
      </motion.a>
      
      <AnimatePresence>
        {isHovered && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            className="absolute bottom-full left-0 transform -translate-x-1/2 mb-2 pointer-events-none z-50"
          >
            <div className="bg-white/90 backdrop-blur-sm text-gray-900 text-sm py-1 px-3 rounded-lg shadow-lg whitespace-nowrap">
              {name}
              <div className="w-2 h-2 bg-white/90 transform rotate-45 absolute left-1/3 -translate-x-1/2 -bottom-1"></div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

// const SkillIcon = ({ icon: Icon, label, color }) => {
//   const [isHovered, setIsHovered] = useState(false);

//   return (
//     <motion.div
//       className="relative group"
//       onHoverStart={() => setIsHovered(true)}
//       onHoverEnd={() => setIsHovered(false)}
//       whileHover={{ scale: 1.1 }}
//       whileTap={{ scale: 0.95 }}
//     >
//       <motion.div
//         className="w-16 h-16 rounded-2xl flex justify-center items-center border-2 border-white/20 text-white/90 backdrop-blur-sm bg-white/5"
//         animate={{
//           borderColor: isHovered ? color : 'rgba(255, 255, 255, 0.2)',
//           color: isHovered ? color : 'rgba(255, 255, 255, 0.9)'
//         }}
//       >
//         <Icon className="w-8 h-8" />
//       </motion.div>
      
//       <AnimatePresence>
//         {isHovered && (
//           <motion.div
//             initial={{ opacity: 0, y: 5 }}
//             animate={{ opacity: 1, y: 0 }}
//             exit={{ opacity: 0, y: 5 }}
//             className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 text-white/80 text-sm whitespace-nowrap"
//           >
//             {label}
//           </motion.div>
//         )}
//       </AnimatePresence>
//     </motion.div>
//   );
// };

const TypewriterText = ({ text }) => (
  <motion.span
    key={text}
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -20 }}
    transition={{ duration: 0.5 }}
    className="text-[#915eff] inline-block font-bold"
  >
    {text}
  </motion.span>
);

const Hero = () => {
  const [currentRole, setCurrentRole] = useState('');
  const [roleIndex, setRoleIndex] = useState(0);
  const [typingIndex, setTypingIndex] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);
  const [blink, setBlink] = useState(true);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  const handleMouseMove = (e) => {
    setMousePosition({
      x: (e.clientX / window.innerWidth - 0.5) * 20,
      y: (e.clientY / window.innerHeight - 0.5) * 20,
    });
  };

  const particlesInit = useCallback(async (engine) => {
    await loadSlim(engine);
  }, []);

  const typingSpeed = 100;
  const deletingSpeed = 50;
  const pauseTime = 1500;

  useEffect(() => {
    let typeTimeout;
    let blinkTimeout = setInterval(() => setBlink((prev) => !prev), 500);

    if (!isDeleting && typingIndex < roles[roleIndex].length) {
      typeTimeout = setTimeout(() => {
        setCurrentRole((prev) => prev + roles[roleIndex].charAt(typingIndex));
        setTypingIndex((prev) => prev + 1);
      }, typingSpeed);
    } else if (isDeleting && typingIndex > 0) {
      typeTimeout = setTimeout(() => {
        setCurrentRole((prev) => prev.slice(0, -1));
        setTypingIndex((prev) => prev - 1);
      }, deletingSpeed);
    } else if (!isDeleting && typingIndex === roles[roleIndex].length) {
      typeTimeout = setTimeout(() => setIsDeleting(true), pauseTime);
    } else if (isDeleting && typingIndex === 0) {
      setIsDeleting(false);
      setRoleIndex((prev) => (prev + 1) % roles.length);
    }

    return () => {
      clearTimeout(typeTimeout);
      clearInterval(blinkTimeout);
    };
  }, [typingIndex, isDeleting, roleIndex]);

  return (
    <section 
      className='relative w-full h-screen mx-auto overflow-hidden bg-gradient-to-b from-[#0f1729] to-[#130f29]'
      onMouseMove={handleMouseMove}
    >
      <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]"></div>
      
      <Particles
        id="tsparticles"
        init={particlesInit}
        options={{
          background: {
            color: {
              value: "transparent",
            },
          },
          particles: {
            number: {
              value: 50,
              density: {
                enable: true,
                value_area: 800,
              },
            },
            color: {
              value: "#915eff",
            },
            shape: {
              type: "circle",
            },
            opacity: {
              value: 0.25,
              random: false,
            },
            size: {
              value: 2,
              random: true,
            },
            line_linked: {
              enable: true,
              distance: 150,
              color: "#915eff",
              opacity: 0.2,
              width: 1,
            },
            move: {
              enable: true,
              speed: 1,
              direction: "none",
              random: true,
              straight: false,
              out_mode: "out",
              bounce: false,
            },
          },
          interactivity: {
            detect_on: "canvas",
            events: {
              onhover: {
                enable: true,
                mode: "bubble",
              },
              onclick: {
                enable: true,
                mode: "push",
              },
              resize: true,
            },
            modes: {
              bubble: {
                distance: 200,
                size: 4,
                duration: 2,
                opacity: 0.4,
              },
              push: {
                particles_nb: 4,
              },
            },
          },
          retina_detect: true,
        }}
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          zIndex: 0
        }}
      />

      <motion.div 
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
        style={{
          transform: `translate(${mousePosition.x}px, ${mousePosition.y}px)`,
        }}
        className="absolute inset-0 top-[160px] max-w-7xl mx-auto flex flex-col items-center justify-start px-6 gap-8 transition-transform duration-200 ease-out"
      >
        <motion.div
          initial={{ scale: 0.5, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="relative w-32 h-32 rounded-full overflow-hidden border-4 border-[#915eff]/50 shadow-lg shadow-[#915eff]/20"
        >
          <img 
            src={me}
            alt="Profile"
            className="w-full h-full object-cover"
          />
          <motion.div 
            className="absolute inset-0" //bg-[#915fff]"
            initial={{ opacity: 0.5 }}
            whileHover={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          />
        </motion.div>

        <div className="text-center z-10 max-w-3xl">
          <motion.h1 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-4xl sm:text-6xl font-bold text-white mb-4 leading-tight"
          >
            Hi, I'm <span className='text-[#915eff] inline-block'>Md. Golam Mostofa</span>
          </motion.h1>

          <motion.p 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-xl sm:text-2xl text-white/80 mb-8"
          >
            A passionate{' '}
            <TypewriterText text={currentRole} />
            <span className={`${blink ? 'opacity-100' : 'opacity-0'} transition-opacity duration-75 text-[#915eff]`}>|</span>
          </motion.p>

          {/* <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="flex flex-wrap justify-center gap-4 mb-12"
          >
            {skills.map((skill, index) => (
              <SkillIcon 
                key={index}
                {...skill}
              />
            ))}
          </motion.div> */}

          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="flex flex-wrap justify-center gap-4"
          >
            {socialLinks.map((link, index) => (
              <SocialLink 
                key={index}
                {...link}
              />
            ))}
          </motion.div>
        </div>
      </motion.div>

      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 1 }}
        className='absolute bottom-10 w-full flex justify-center items-center'
      >
        <a 
          href='#about' 
          className="group relative p-4"
          aria-label="Scroll to About section"
        >
          <motion.div
            animate={{
              y: [0, 10, 0],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              repeatType: 'loop',
            }}
            className="text-white/50 group-hover:text-[#915eff] transition-colors duration-300"
          >
            <FaArrowDown className="w-6 h-6" />
          </motion.div>
        </a>
      </motion.div>
    </section>
  );
};

export default Hero;