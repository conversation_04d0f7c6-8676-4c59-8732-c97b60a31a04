import { DynamicTool } from "@langchain/core/tools";
import { portfolioData } from "../constants/my_database";

export function createPortfolioTools() {
    return [
        // Introduction Tool
        new DynamicTool({
            name: "getIntroduction",
            description: "Get Md. Golam Mostofa's detailed introduction including current position, skills, and key projects",
            func: async () => {
                try {
                    return JSON.stringify(portfolioData.introduction)
                } catch (error) {
                    console.error('Error in getIntroduction:', error);
                    throw new Error('Failed to retrieve introduction');
                }
            }
        }),

        // Current Position Tool
        new DynamicTool({
            name: "getCurrentPosition",
            description: "Get detailed information about current position and responsibilities",
            func: async () => {
                try {
                    // const position = portfolioData.introduction.current_position;
                    return JSON.stringify(portfolioData.introduction.current_position);
                } catch (error) {
                    console.error('Error in getCurrentPosition:', error);
                    throw new Error('Failed to retrieve current position');
                }
            }
        }),

        // Skills Tool
        new DynamicTool({
            name: "getTechnicalSkills",
            description: "Get information about technical skills in AI/ML and backend development",
            func: async (domain = 'all') => {
                try {
                    // const skills = portfolioData.introduction.technical_skills;
                    // if (domain.toLowerCase() === 'ai' || domain.toLowerCase() === 'ai_ml') {
                    //     return JSON.stringify({ ai_ml: skills.ai_ml }, null, 2);
                    // }
                    // if (domain.toLowerCase() === 'backend') {
                    //     return JSON.stringify({ backend: skills.backend }, null, 2);
                    // }
                    return JSON.stringify(portfolioData.introduction.technical_skills);
                } catch (error) {
                    console.error('Error in getTechnicalSkills:', error);
                    throw new Error('Failed to retrieve technical skills');
                }
            }
        }),

        // Experience Tool
        new DynamicTool({
            name: "getExperience",
            description: "Get detailed work experience information",
            func: async () => {
                try {
                    let experiences = portfolioData.experiences;
                    // if (company) {
                    //     experiences = experiences.filter(exp => 
                    //         exp.company_name.toLowerCase().includes(company.toLowerCase())
                    //     );
                    // }
                    return JSON.stringify(experiences);
                } catch (error) {
                    console.error('Error in getExperience:', error);
                    throw new Error('Failed to retrieve experience information');
                }
            }
        }),

        // Projects Tool
        new DynamicTool({
            name: "getProjects",
            description: "Get information about projects, optionally filtered by technology or tag",
            func: async (tag = '') => {
                try {
                    let projects = portfolioData.projects;
                    if (tag) {
                        projects = projects.filter(project => 
                            project.tags.some(t => t.toLowerCase().includes(tag.toLowerCase()))
                        );
                    }
                    return JSON.stringify(projects);
                } catch (error) {
                    console.error('Error in getProjects:', error);
                    throw new Error('Failed to retrieve projects');
                }
            }
        }),

        // Education Tool
        new DynamicTool({
            name: "getEducation",
            description: "Get education history and achievements",
            func: async () => {
                try {
                    const education = portfolioData.education;
                    return JSON.stringify(education);
                } catch (error) {
                    console.error('Error in getEducation:', error);
                    throw new Error('Failed to retrieve education information');
                }
            }
        }),

        // Publications Tool
        new DynamicTool({
            name: "getPublications",
            description: "Get Md. Golam Mostofa's research publications information",
            func: async () => {
                try {
                    return JSON.stringify(portfolioData.publications, null, 2);
                } catch (error) {
                    console.error('Error in getPublications:', error);
                    throw new Error('Failed to retrieve publications');
                }
            }
        }),

        // Testimonials Tool
        new DynamicTool({
            name: "getTestimonials",
            description: "Get testimonials from references",
            func: async () => {
                try {
                    return JSON.stringify(portfolioData.testimonials);
                } catch (error) {
                    console.error('Error in getTestimonials:', error);
                    throw new Error('Failed to retrieve testimonials');
                }
            }
        }),

        // Technologies Tool
        new DynamicTool({
            name: "getTechnologies",
            description: "Get list of all technologies and tools used",
            func: async () => {
                try {
                    return JSON.stringify({
                        technologies: portfolioData.technologies,
                        count: portfolioData.technologies.length
                    }, null, 2);
                } catch (error) {
                    console.error('Error in getTechnologies:', error);
                    throw new Error('Failed to retrieve technologies');
                }
            }
        }),

        // Services Tool
        new DynamicTool({
            name: "getServices",
            description: "Get list of professional services offered",
            func: async () => {
                try {
                    return JSON.stringify({
                        services: portfolioData.services,
                        count: portfolioData.services.length
                    }, null, 2);
                } catch (error) {
                    console.error('Error in getServices:', error);
                    throw new Error('Failed to retrieve services');
                }
            }
        }),

        // Social Links Tool
        new DynamicTool({
            name: "getSocialLinks",
            description: "Get all social media and professional profile links",
            func: async () => {
                try {
                    return JSON.stringify({
                        profiles: portfolioData.social_links,
                        count: portfolioData.social_links.length
                    }, null, 2);
                } catch (error) {
                    console.error('Error in getSocialLinks:', error);
                    throw new Error('Failed to retrieve social links');
                }
            }
        }),

        // Contact Information Tool
        new DynamicTool({
            name: "getContactInfo",
            description: "Get contact information",
            func: async () => {
                try {
                    return JSON.stringify(portfolioData.introduction.contact, null, 2);
                } catch (error) {
                    console.error('Error in getContactInfo:', error);
                    throw new Error('Failed to retrieve contact information');
                }
            }
        })
    ];
}

export default createPortfolioTools;