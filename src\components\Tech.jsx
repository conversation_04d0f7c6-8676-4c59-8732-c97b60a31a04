
import { SectionWrapper } from "../hoc"
import { technologies } from "../constants"



const Tech = () => {
  return (
    <div className="flex flex-row flex-wrap justify-center gap-10">
      {technologies.map((technology) => (
        <div 
          key={technology.name} 
          className="w-28 h-28 relative group"
        >
          <div 
            className="absolute inset-0 rounded-full bg-white/5 backdrop-blur-sm 
            transform transition-all duration-300 ease-in-out
            group-hover:scale-110 group-hover:bg-white/10"
          >
            <div className="w-full h-full flex flex-col items-center justify-center">
              <img 
                src={technology.icon}
                alt={technology.name}
                className="w-16 h-16 object-contain transition-transform duration-300 
                group-hover:scale-110"
              />
              <p className="mt-2 text-sm text-gray-300 opacity-0 group-hover:opacity-100 
                transition-opacity duration-300">
                {technology.name}
              </p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default SectionWrapper(Tech, '')