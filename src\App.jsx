import React, { Suspense } from 'react';
import { BrowserRouter, Routes, Route } from "react-router-dom";
import {
  About,
  Contact,
  Experience,
  Feedbacks,
  Hero,
  Navbar,
  Tech,
  Works,
  StarsCanvas,
  Publications,
  Education,
  Photos,
  Bootcamps,
  FloatingChat,
  AnimatedChatIcon,
} from './components';
import RouterError from './RouterError';

// Loading component
const LoadingSpinner = () => (
  <div className="flex items-center justify-center h-screen">
    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-white" />
  </div>
);

// Main content for home page
const MainContent = () => (
  <>
    <div className="bg-hero-pattern bg-cover bg-no-repeat bg-center">
      <Hero />
    </div>
    <About />
    <Experience />
    <Tech />
    <Education />
    <Works />
    <Publications />
    <Feedbacks />
    <div className="relative z-0">
      <Contact />
      <StarsCanvas />
    </div>
  </>
);

// Works page content
const WorksContent = () => (
  <>
    <Experience />
    <Tech />
  </>
);

// Contact page content
const ContactContent = () => (
  <div className="relative z-0">
    <Contact />
    <StarsCanvas />
  </div>
);

const App = () => {

  return (
    <BrowserRouter>
      <div className="relative z-0 bg-primary">
        <Navbar />
        <Suspense fallback={<LoadingSpinner />}>
          <Routes>
            <Route path="/" element={<MainContent />} />
            <Route path="/photos" element={<Photos />} />
            <Route path="/about" element={<About />} />
            <Route path="/works" element={<WorksContent />} />
            <Route path="/educations" element={<Education />} />
            <Route path="/projects" element={<Works />} />
            <Route path="/publications" element={<Publications />} />
            <Route path="/bootcamps" element={<Bootcamps />} />
            <Route path="/contact" element={<ContactContent />} />
            <Route path="*" element={<RouterError />} />
          </Routes>
        </Suspense>
        <FloatingChat />
      </div>
    </BrowserRouter>
  );
};

export default App;