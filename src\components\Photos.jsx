import { motion } from 'framer-motion';
import PropTypes from 'prop-types';
import { useState, useEffect, useRef } from 'react';
import { styles } from '../styles';
import { fadeIn, textVariant } from '../utils/motion';
import { SectionWrapper } from '../hoc';
import { photos } from '../constants';
import { X } from 'lucide-react';

const ImageModal = ({ image, title, onClose }) => {
  const modalRef = useRef(null);

  const handleClick = (e) => {
    // Close if click is outside the image
    if (modalRef.current === e.target) {
      onClose();
    }
  };

  useEffect(() => {
    // Add Escape key listener
    const handleEscKey = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscKey);
    return () => document.removeEventListener('keydown', handleEscKey);
  }, [onClose]);

  return (
    <div
      ref={modalRef}
      onClick={handleClick}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80 p-4 cursor-pointer"
    >
      <div className="relative max-w-7xl w-full">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-white hover:text-gray-300 z-50"
        >
          <X size={24} />
        </button>
        <img
          src={image}
          alt={title}
          className="max-h-[90vh] w-full object-contain rounded-lg"
          onClick={(e) => e.stopPropagation()} // Prevent closing when clicking the image
        />
      </div>
    </div>
  );
};

ImageModal.propTypes = {
  image: PropTypes.string.isRequired,
  title: PropTypes.string.isRequired,
  onClose: PropTypes.func.isRequired,
};

const PhotoCard = ({ title, description, image, tags }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <motion.div
        variants={fadeIn("up", "spring", 0.5, 0.75)}
        className="bg-tertiary p-5 rounded-2xl sm:w-[360px] w-full"
      >
        <div 
          className="relative w-full h-64 cursor-pointer"
          onClick={() => setIsModalOpen(true)}
        >
          <img
            src={image}
            alt={title}
            className="w-full h-full object-cover rounded-2xl transition-transform duration-300 hover:scale-[1.02]"
          />
        </div>

        <div className="mt-5">
          <h3 className="text-white font-bold text-2xl">{title}</h3>
          <p className="mt-2 text-secondary text-sm">{description}</p>
        </div>

        <div className="mt-4 flex flex-wrap gap-2">
          {tags.map((tag) => (
            <p
              key={tag}
              className="text-sm text-white-100 bg-black-200 px-2 py-1 rounded-lg"
            >
              #{tag}
            </p>
          ))}
        </div>
      </motion.div>

      {isModalOpen && (
        <ImageModal
          image={image}
          title={title}
          onClose={() => setIsModalOpen(false)}
        />
      )}
    </>
  );
};

PhotoCard.propTypes = {
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  image: PropTypes.string.isRequired,
  tags: PropTypes.arrayOf(PropTypes.string).isRequired,
};

const Photos = () => {
  return (
    <div className="mt-20 padding-x padding-y max-w-7xl mx-auto relative z-0">
      <motion.div variants={textVariant()}>
        <p className={styles.sectionSubText}>My Gallery</p>
        <h2 className={styles.sectionHeadText}>Photos.</h2>
      </motion.div>

      <div className="w-full flex">
        <motion.p
          variants={fadeIn("", "", 0.1, 1)}
          className="mt-3 text-secondary text-lg max-w-3xl leading-8"
        >
          A collection of my photography work, showcasing various styles and subjects.
        </motion.p>
      </div>

      <div className="mt-20 flex flex-wrap gap-7">
        {photos.map((photo, index) => (
          <PhotoCard key={`photo-${index}`} {...photo} />
        ))}
      </div>
    </div>
  );
};

export default SectionWrapper(Photos, "photos");