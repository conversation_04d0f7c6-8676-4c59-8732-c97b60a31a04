import React, { useState } from "react";
import { motion } from 'framer-motion';
import { BookOpen, Calendar, ExternalLink, ChevronDown, Award, Quote } from "lucide-react";
import { styles } from '../styles';
import { fadeIn, textVariant } from '../utils/motion';
import { SectionWrapper } from '../hoc';
import { publications } from '../constants';




const PublicationCard = ({ publication, index }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showCitation, setShowCitation] = useState(false);

  return (
    <motion.div 
      variants={fadeIn("up", "spring", index * 0.5, 0.75)}
      className="bg-tertiary p-5 rounded-2xl w-full cursor-pointer group mb-6"
      onClick={() => setIsExpanded(!isExpanded)}
    >
      <div className="space-y-4">
        {/* Header with Icon */}
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <BookOpen className="w-5 h-5 text-[#915eff]" />
            <h3 className="text-white font-bold text-[24px] group-hover:text-[#915eff] transition-colors">
              {publication.title}
            </h3>
          </div>

          <div className="flex gap-2">
            <div 
              onClick={(e) => {
                e.stopPropagation();
                setShowCitation(!showCitation);
              }}
              className="black-gradient w-10 h-10 rounded-full flex justify-center items-center cursor-pointer"
            >
              <Quote className="w-1/2 h-1/2 text-white" />
            </div>
            <div 
              onClick={(e) => {
                e.stopPropagation();
                window.open(publication.link, "_blank");
              }}
              className="black-gradient w-10 h-10 rounded-full flex justify-center items-center cursor-pointer"
            >
              <ExternalLink className="w-1/2 h-1/2 text-white" />
            </div>
          </div>
        </div>

        {/* Citation Popup */}
        {showCitation && (
          <motion.div 
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-black/90 p-4 rounded-lg text-secondary text-[14px] cursor-text"
            onClick={(e) => e.stopPropagation()}
          >
            <p className="mb-2 font-semibold">Citation:</p>
            <p className="select-all">{publication.citation}</p>
          </motion.div>
        )}

        {/* Main Info */}
        <div className="flex flex-wrap gap-4 text-secondary text-[14px]">
          <div className="flex items-center gap-1">
            <Award className="w-4 h-4" />
            {publication.venue}
          </div>
          <div className="flex items-center gap-1">
            <Calendar className="w-4 h-4" />
            {publication.year}
          </div>
          <div className="flex items-center gap-1">
            <Quote className="w-4 h-4" />
            {publication.citationCount} citations
          </div>
        </div>

        {/* Authors */}
        <div className="text-secondary text-[14px]">
          {publication.authors.join(", ")}
        </div>

        {/* Expand/Collapse Section */}
        <motion.div 
          initial={false}
          animate={{ height: isExpanded ? "auto" : 0 }}
          className="overflow-hidden"
        >
          <p className="text-secondary text-[14px] leading-[30px]">
            {publication.description}
          </p>
        </motion.div>

        {/* Tags */}
        <div className="mt-4 flex flex-wrap gap-2">
          {publication.tags.map((tag) => (
            <p key={tag.name} className={`text-[14px] ${tag.color}`}>
              #{tag.name}
            </p>
          ))}
        </div>

        {/* Expand Icon */}
        <div className="flex justify-center">
          <ChevronDown 
            className={`w-5 h-5 text-secondary transition-transform duration-300
              ${isExpanded ? 'rotate-180' : ''}`}
          />
        </div>
      </div>
    </motion.div>
  );
};

const Publications = () => {
  return (
    <div className="w-full max-w-6xl mx-auto">
      {/* Header */}
      <motion.div variants={textVariant()}>
        <p className={styles.sectionSubText}>My research</p>
        <h2 className={styles.sectionHeadText}>Publications.</h2>
      </motion.div>

      <div className="w-full">
        <motion.p 
          variants={fadeIn("", "", 0.1, 1)}
          className="mt-4 text-secondary text-[17px] max-w-3xl leading-[30px]"
        >
          The following publications showcase my research contributions in machine learning, 
          computer vision, and related fields. Each publication represents significant 
          advancements and novel approaches to solving complex problems in the field.
        </motion.p>
      </div>

      <div className="mt-20">
        {publications.map((publication, index) => (
          <PublicationCard
            key={publication.id}
            index={index}
            publication={publication}
          />
        ))}
      </div>
    </div>
  );
};

export default SectionWrapper(Publications, "publications");




