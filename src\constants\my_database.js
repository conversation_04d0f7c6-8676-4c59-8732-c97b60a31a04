export const portfolioData = {
    introduction: {
        name: "<PERSON><PERSON><PERSON>",
        nickname: "<PERSON><PERSON>",
        roles: ["AI Specialist", "Machine Learning Engineer", "Backend Developer", "Competitive Programmer"],
        current_position: {
            company: "Bizzntek Ltd",
            responsibilites: [
                "Lead and mentor a team of 3 engineers,overseeing project planning,task delegation,and technical guidance.",
                "Architect and implement end‑to‑end automation solutions using Microsoft Power Automate to reduce manual processes and improveworkflowefficiency.",
                "Design and deployconversational Copilot agents through Microsoft Copilot Studio to assist users and automate routine inquiries.",
                "Collaborate cross‑functionally with business stakeholders to understand process requirements and deliver customized automationstrategies.",
                "Ensurequality, security, and performance of deployed solutions through regular testing, review, and optimization.",
                "Coordinate documentation and knowledge sharing within the team to maintain best practices and improve productivity."
            ],
            technologies: [
                "Generative AI",
                "Build LLM",
                "Fine Tuning",
                "LangChain",
                "NLP",
                "PyTorch",
                "Deep Learning",
                "Federated Learning",
                "FastAPI",
                "Docker",
                "AWS EC2",
                "PostgreSQL"
            ]
        },
        technical_skills: {
            ai_ml: ["PyTorch", "TensorFlow", "Langchain", "LiteLLM", "Mem0"],
            backend: ["Django", "FAST API", "AWS EC2", "PostgreSQL"]
        },
        key_projects: ["Astra LLM", "RAG System"],
        education: {
            degree: "B.Sc. in Computer Science and Engineering",
            institution: "Dhaka University of Engineering and Technology (DUET)",
            research: {
                field: "clustering algorithms",
                paper_link: "https://dl.acm.org/doi/10.1145/3605098.3636188"
            }
        },
        profiles: {
            leetcode: "https://leetcode.com/u/golammostofa10001/",
            kaggle: "https://www.kaggle.com/golammostofas",
            google_scholar: "https://scholar.google.com/citations?user=mxW1qOoAAAAJ",
            linkedin: "https://www.linkedin.com/in/mdgolammostofa705/"
        },
        contact: {
            email: "<EMAIL>"
        }
    },
    services: [
        "AI Model Developer",
        "LLM Engineer",
        "Data Scientists",
        "Backend Developer"
    ],
    technologies: [
        "LangChain",
        "LiteLLM",
        "Mem0",
        "Pytorch",
        "Tensorflow",
        "Django",
        "Django REST",
        "JavaScript",
        "HTML 5",
        "CSS 3",
        "React JS",
        "Tailwind CSS",
        "MongoDB",
        "PostgreSQL",
        "ChromaDB",
        "git",
        "GitHub",
        "docker",
        "AWS EC2",
        "Azure"
    ],
    experiences: [
        {
            title: "Lead AI Engineer",
            company_name: "Bizzntek Ltd, Dhaka, BD",
            date: "Apr 2025 - Present",
            points: [
                "Lead and mentor a team of 3 engineers,overseeing project planning,task delegation,and technical guidance.",
                "Architect and implement end‑to‑end automation solutions using Microsoft Power Automate to reduce manual processes and improveworkflowefficiency.",
                "Design and deployconversational Copilot agents through Microsoft Copilot Studio to assist users and automate routine inquiries.",
                "Collaborate cross‑functionally with business stakeholders to understand process requirements and deliver customized automationstrategies.",
                "Ensurequality, security, and performance of deployed solutions through regular testing, review, and optimization.",
                "Coordinate documentation and knowledge sharing within the team to maintain best practices and improve productivity."
            ]
        },
        {
            title: "Machine Learning Engineer",
            company_name: "Devolved AI, LA, USA",
            date: "Sep 2023 - Mar 2025",
            points: [
                "Large data sets were pre‑processeded for LLMs using cleaning, feature engineering, normalization, tokenization, and text encoding techniques.",
                "Creating evaluation and instruction data sets to support the ongoing development and validation of models.",
                "Improve task‑specific results and overall accuracy by pre‑training, optimizing, and fine‑tuning models to meet performance criteria.",
                "Evaluate the Performance of Large Language Models(LLMs).",
                "Deploy Large Language Models (LLMs)"
            ]
        },
        {
            title: "LLM Engineer",
            company_name: "Serenus One, LA, USA",
            date: "Jun 2024 - Aug 2023 ",
            points: [
                "Fine-tune and optimize models for performance and accuracy.",
                "Improvement Chatbot performance.",
                "RAG system implementation",
                "API endpoint making",
                "Collaborate with software engineers to integrate AI models into production systems."
            ]
        },
        {
            title: "Notebooks Expert",
            company_name: "Kaggle",
            date: "Aug 2022 - Present",
            points: [
                "I enhanced my data science, Machine Learning, and NLP skills through Kaggle competitions.",
                "I am top 5% (44/955) on \"Improve a Fixed Model the Data‑Centric Way!\" competition."
            ]
        },
        {
            title: "Problem Solver",
            company_name: "LeetCode",
            date: "Aug 2022 - Present",
            points: [
                "I have solved over 330 problems on LeetCode.",
                "Maximum Contest Rating: 1433"
            ]
        }
    ],
    testimonials: [
        {
            testimonial: "He is an exceptional student whose dedication, creativity, and leadership consistently inspire excellence in everything he do.",
            name: "Prof. Dr. MD. Obaidur Rahman",
            designation: "Ex-Head of CSE Department",
            company: "DUET(<EMAIL>)",
            image: "obidur"
        },
        {
            testimonial: "He has consistently demonstrated exceptional dedication, leadership, and a proactive approach, making him an invaluable member of any team.",
            name: "Dr. Momotaz Begum",
            designation: "Professor, CSE Department",
            company: "DUET(<EMAIL>)",
            image: "momtaz"
        },
        {
            testimonial: "As an advisor, I have been impressed by he unwavering dedication, intellectual curiosity, and ability to excel in both academic and professional pursuits.",
            name: "Sumaya Kazary",
            designation: "Assistant Professor, CSE Department",
            company: "DUET(<EMAIL>)",
            image: "kazary"
        }
    ],
    projects: [
        {
            name: "Athena-2",
            description: "Athena is an advanced large language model fine-tuned from the Llama3.1 8b LLM, optimized for high performance and specialized use cases.",
            tags: ["Fine Tuning", "Federated Learning"],
            source_code_link: "https://athena.devolvedai.com/"
        },
        {
            name: "Sara's AI Assistant",
            description: "This is an LLM-based AI assistant that can perform mental health. It is fintuned from ChatGPT 4o-mini, Llama3.2, and Claude",
            tags: ["Fine Tuning", "LiteLLM", "Mem0", "Langchain", "FastAPI"],
            source_code_link: "https://guide.serenus.one/"
        },
        {
            name: "Astra LLM",
            description: "Astra LLM is a base model with 1.4 billion parameters, designed for foundational AI model development.",
            tags: ["Build LLM from Scratch", "PyTorch", "LLM Architecture"],
            source_code_link: "https://www.kaggle.com/models/golammostofas/astra/"
        },
        {
            name: "Automatic MCQ Generator",
            description: "An AI-powered tool that automatically generates multiple-choice questions from given text.",
            tags: ["LangChain", "Gemini API"],
            source_code_link: "https://github.com/shuvo881/Automatic-MCQ-Generate"
        },
        {
            name: "AI Chat Bot",
            description: "An AI chatbot fine-tuned from the Llama3.2 8b Instruction LLM, enabling interactive conversational AI.",
            tags: ["Django", "REST-API", "React JS"],
            source_code_link: "https://github.com/shuvo881/AI_Chat_Bot"
        },
        {
            name: "Agricultural AI Doctor",
            description: "A computer vision-based AI model that detects plant diseases and suggests remedies based on leaf analysis.",
            tags: ["Machine Learning", "Django", "Bootstrap CSS"],
            source_code_link: "https://github.com/shuvo881/Agricultural_AI_Doctor"
        },
        {
            name: "Face Mask Detect Door System",
            description: "An AI-based security system that detects whether a person is wearing a mask and controls door access accordingly.",
            tags: ["Keras", "OpenCV", "Supervised Machine Learning"],
            source_code_link: "https://github.com/shuvo881/Face-Mask-Detect-Door-System"
        },
        {
            name: "Emotion-Based Music System",
            description: "An AI-driven system that plays music based on facial emotions detected through deep learning.",
            tags: ["DeepFace", "OpenCV"],
            source_code_link: "https://github.com/shuvo881/Emusic"
        },
        {
            name: "SK Best Quality",
            description: "An e-commerce platform with product management, cart updates, and order processing functionalities.",
            tags: ["Django", "REST-API", "Docker", "EC2", "JavaScript", "Tailwind CSS"],
            source_code_link: "http://************:8000/"
        },
        {
            name: "UOMP",
            description: "An online marketplace where users can sell and purchase products efficiently.",
            tags: ["Django", "PostgreSQL", "Docker", "EC2", "JavaScript", "Tailwind CSS"],
            source_code_link: "https://github.com/shuvo881/Urgent_Online_Marketplace"
        }
    ],
    publications: [
        {
            id: 1,
            title: "M-DBSCAN: Modified DBSCAN Clustering Algorithm for Detecting and Controlling Outliers",
            venue: "SAC '24: 39th ACM/SIGAPP Symposium on Applied Computing (Avila, Spain)",
            year: 2024,
            authors: [
                "Momotaz Begum",
                "Mehedi Hasan Shuvo",
                "Md. Golam Mostofa",
                "Abm Kamrul Islam Riad",
                "Md Arabin Islam Talukder",
                "Mst Shapna Akter",
                "Hossain Shahriar"
            ],
            impact: "Featured in Top ML Publications",
            link: "https://dl.acm.org/doi/10.1145/3605098.3636188",
            description: "Outlier reduction is crucial in computer science for improving data quality, analysis accuracy, and modeling robustness...",
            tags: [
                { name: "DBSCAN"},
                { name: "Unsupervised Learning"}
            ]
        }
    ],
    education: [
        {
            school: "Dhaka University of Engineering and Technology - DUET",
            degree: "BSc in Computer Science and Engineering",
            duration: "2018 - 2023",
            description: "Specialized in Artificial Intelligence and Machine Learning with a focus on Deep Learning and Natural Language Processing...",
            achievements: [
                "Published research paper on M-DBSCAN",
                "Received DRC FEST - 2022 Award"
            ],
            courses: [
                "Deep Learning",
                "Natural Language Processing",
                "Computer Vision",
                "Distributed Systems",
                "Machine Learning",
                "AI Ethics"
            ]
        },
        {
            school: "Gopalganj Polytechnic Institute",
            degree: "Diploma in Computer Technology",
            duration: "2014 - 2018",
            description: "Completed undergraduate studies with a focus on algorithms and theoretical computer science...",
            achievements: ["Undergraduate basic computer science"],
            courses: [
                "Algorithms",
                "Data Structures",
                "Operating Systems",
                "Computer Networks",
                "Database Systems",
                "Software Engineering"
            ]
        }
    ],
    social_links: [
        {
            href: "https://www.linkedin.com/in/mdgolammostofa705/",
            name: "LinkedIn"
        },
        {
            href: "https://github.com/shuvo881",
            name: "GitHub"
        },
        {
            href: "https://scholar.google.com/citations?user=mxW1qOoAAAAJ",
            name: "Google Scholar"
        },
        {
            href: "https://www.kaggle.com/golammostofas",
            name: "Kaggle"
        },
        {
            href: "https://leetcode.com/u/golammostofa10001/",
            name: "LeetCode"
        },
        {
            href: "https://medium.com/@golammostofa10001",
            name: "Medium"
        }
    ]
};

export const navLinks = [
    {
        id: "about",
        title: "About",
    },
    {
        id: "work",
        title: "Work",
    },
    {
        id: "contact",
        title: "Contact",
    },
];

export default portfolioData;