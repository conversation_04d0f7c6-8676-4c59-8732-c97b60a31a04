import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { styles } from '../styles';
import { SectionWrapper } from '../hoc';
import {education} from '../constants'
import { Calendar, BookOpen, Award, ChevronDown, ChevronUp } from 'lucide-react';

const EducationCard = ({ school, degree, duration, description, courses, achievements, image }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <motion.div 
      className="w-full bg-white/5 backdrop-blur-lg rounded-xl p-6 hover:bg-white/10 transition-all duration-300"
      whileHover={{ y: -5 }}
    >
      <div className="flex items-start gap-4">
        <div className="w-16 h-16 rounded-lg overflow-hidden flex-shrink-0">
          <img
            src={image}
            alt={school}
            className="w-full h-full object-cover"
          />
        </div>
        
        <div className="flex-1">
          <h3 className="text-white font-bold text-xl">{school}</h3>
          <p className="text-secondary mt-1 flex items-center gap-2">
            <BookOpen className="w-4 h-4" />
            {degree}
          </p>
          <p className="text-secondary/80 mt-1 flex items-center gap-2">
            <Calendar className="w-4 h-4" />
            {duration}
          </p>
        </div>
      </div>

      <div 
        className={`mt-4 overflow-hidden transition-all duration-300 ${
          isExpanded ? 'max-h-96' : 'max-h-20'
        }`}
      >
        <p className="text-secondary/90 text-sm leading-relaxed">
          {description}
        </p>

        {achievements && achievements.length > 0 && (
          <div className="mt-4">
            <h4 className="text-white font-semibold flex items-center gap-2">
              <Award className="w-4 h-4" />
              Key Achievements
            </h4>
            <ul className="mt-2 space-y-2">
              {achievements.map((achievement, index) => (
                <li 
                  key={index}
                  className="text-secondary/80 text-sm flex items-start gap-2"
                >
                  <span className="text-primary mt-1">•</span>
                  {achievement}
                </li>
              ))}
            </ul>
          </div>
        )}

        <div className="mt-4">
          <h4 className="text-white font-semibold">Key Courses</h4>
          <div className="mt-2 flex flex-wrap gap-2">
            {courses.map((course, index) => (
              <span
                key={index}
                className="px-3 py-1 rounded-full text-xs bg-primary/20 text-slate-400"
              >
                {course}
              </span>
            ))}
          </div>
        </div>
      </div>

      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="mt-4 w-full flex items-center justify-center gap-1 text-secondary/80 hover:text-white transition-colors text-sm"
      >
        {isExpanded ? (
          <>Show Less <ChevronUp className="w-4 h-4" /></>
        ) : (
          <>Show More <ChevronDown className="w-4 h-4" /></>
        )}
      </button>
    </motion.div>
  );
};

const Education = () => {
  

  return (
    <div className="w-full">
      <div>
        <p className={styles.sectionSubText}>Academic Journey.</p>
        <h2 className={styles.sectionHeadText}>Education.</h2>
      </div>

      <div className="mt-4 mb-12">
        <p className="text-secondary text-lg max-w-3xl">
          My academic background has provided me with strong theoretical foundations
          and practical experience in computer science, particularly in AI and machine learning.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {education.map((edu, index) => (
          <EducationCard key={index} {...edu} />
        ))}
      </div>
    </div>
  );
};

export default SectionWrapper(Education, "education");