import { motion } from 'framer-motion';
import PropTypes from 'prop-types';
import { useState } from 'react';
import { styles } from '../styles';
import { fadeIn, textVariant } from '../utils/motion';
import { SectionWrapper } from '../hoc';
import { bootcamps } from '../constants';
import { Calendar, Users, Clock, Play, X, Video } from 'lucide-react';

const VideoModal = ({ videoUrl, title, onClose }) => {
  return (
    <div
      onClick={onClose}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80 p-4 cursor-pointer"
    >
      <div className="relative max-w-4xl w-full bg-tertiary rounded-lg p-2">
        <button
          onClick={onClose}
          className="absolute -top-10 right-0 text-white hover:text-gray-300 z-50"
        >
          <X size={24} />
        </button>
        <div className="relative pt-[56.25%]">
          <iframe
            className="absolute top-0 left-0 w-full h-full rounded-lg"
            src={videoUrl.replace('youtube.com/watch?v=', 'youtube.com/embed/')}
            title={title}
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            onClick={(e) => e.stopPropagation()}
          />
        </div>
      </div>
    </div>
  );
};

VideoModal.propTypes = {
  videoUrl: PropTypes.string.isRequired,
  title: PropTypes.string.isRequired,
  onClose: PropTypes.func.isRequired,
};

const BootcampCard = ({ 
  title, 
  description, 
  duration, 
  startDate, 
  participants, 
  topics, 
  image,
  videoUrl,
  recordedSessions 
}) => {
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);

  return (
    <>
      <motion.div
        variants={fadeIn("up", "spring", 0.5, 0.75)}
        className="bg-tertiary p-5 rounded-2xl sm:w-[360px] w-full"
      >
        <div className="relative w-full h-48 group">
          <img
            src={image}
            alt={title}
            className="w-full h-full object-cover rounded-2xl"
          />
          {videoUrl && (
            <button
              onClick={() => setIsVideoModalOpen(true)}
              className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"
            >
              <Play size={48} className="text-white" />
            </button>
          )}
        </div>

        <div className="mt-5">
          <h3 className="text-white font-bold text-2xl">{title}</h3>
          <p className="mt-2 text-secondary text-sm">{description}</p>
        </div>

        <div className="mt-4 space-y-2">
          <div className="flex items-center gap-2 text-white-100">
            <Calendar size={16} />
            <span className="text-sm">{startDate}</span>
          </div>
          <div className="flex items-center gap-2 text-white-100">
            <Clock size={16} />
            <span className="text-sm">{duration}</span>
          </div>
          <div className="flex items-center gap-2 text-white-100">
            <Users size={16} />
            <span className="text-sm">{participants} participants</span>
          </div>
        </div>

        {recordedSessions && recordedSessions.length > 0 && (
          <div className="mt-4">
            <h4 className="text-white text-sm font-semibold mb-2">Recorded Sessions:</h4>
            <div className="space-y-2">
              {recordedSessions.map((session, index) => (
                <a
                  key={index}
                  href={session.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 text-white-100 hover:text-white transition-colors duration-300"
                >
                  <Video size={16} />
                  <span className="text-sm truncate">{session.title}</span>
                </a>
              ))}
            </div>
          </div>
        )}

        <div className="mt-4 flex flex-wrap gap-2">
          {topics.map((topic) => (
            <span
              key={topic}
              className="text-sm text-white-100 bg-black-200 px-2 py-1 rounded-lg"
            >
              {topic}
            </span>
          ))}
        </div>
      </motion.div>

      {isVideoModalOpen && (
        <VideoModal
          videoUrl={videoUrl}
          title={title}
          onClose={() => setIsVideoModalOpen(false)}
        />
      )}
    </>
  );
};

BootcampCard.propTypes = {
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  duration: PropTypes.string.isRequired,
  startDate: PropTypes.string.isRequired,
  participants: PropTypes.number.isRequired,
  topics: PropTypes.arrayOf(PropTypes.string).isRequired,
  image: PropTypes.string.isRequired,
  videoUrl: PropTypes.string,
  recordedSessions: PropTypes.arrayOf(
    PropTypes.shape({
      title: PropTypes.string.isRequired,
      url: PropTypes.string.isRequired,
    })
  ),
};

const Bootcamps = () => {
  return (
    <div className="mt-20 padding-x padding-y max-w-7xl mx-auto relative z-0">
      <motion.div variants={textVariant()}>
        <p className={styles.sectionSubText}>Training Programs</p>
        <h2 className={styles.sectionHeadText}>Bootcamps.</h2>
      </motion.div>

      <div className="w-full flex">
        <motion.p
          variants={fadeIn("", "", 0.1, 1)}
          className="mt-3 text-secondary text-lg max-w-3xl leading-8"
        >
          Explore our intensive bootcamp programs designed to help you master new skills
          and advance your career. Each bootcamp offers hands-on experience and expert guidance.
        </motion.p>
      </div>

      <div className="mt-20 flex flex-wrap gap-7">
        {bootcamps.map((bootcamp, index) => (
          <BootcampCard key={`bootcamp-${index}`} {...bootcamp} />
        ))}
      </div>
    </div>
  );
};

export default SectionWrapper(Bootcamps, "bootcamps");