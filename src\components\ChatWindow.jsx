import React, { useState, useEffect, useCallback } from 'react';
import { X, Bot, SendHorizonal, Loader2 } from 'lucide-react';
import { marked } from 'marked';
import DOMPurify from 'dompurify';
import { createPortfolioAssistant } from '../utils/portfolioAssistant';
import { markdownStyles } from '../constants/markdown';

// SVG Component for consistent usage
const SendIcon = ({ className }) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    width="12" 
    height="12" 
    viewBox="0 0 24 24" 
    fill="none" 
    stroke="currentColor" 
    strokeWidth="2" 
    strokeLinecap="round" 
    strokeLinejoin="round" 
    className={className}
  >
    <path d="M3.714 3.048a.498.498 0 0 0-.683.627l2.843 7.627a2 2 0 0 1 0 1.396l-2.842 7.627a.498.498 0 0 0 .682.627l18-8.5a.5.5 0 0 0 0-.904z"/>
    <path d="M6 12h16"/>
  </svg>
);

const TypingIndicator = () => (
  <div className="flex items-start space-x-2">
    <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center">
      <Loader2 size={18} className="text-white animate-spin" />
    </div>
    <div className="bg-gray-800 py-2 px-3 rounded-lg">
      <div className="flex space-x-1">
        <div className="w-1.5 h-1.5 bg-gray-400 rounded-full animate-pulse" />
        <div className="w-1.5 h-1.5 bg-gray-400 rounded-full animate-pulse delay-150" />
        <div className="w-1.5 h-1.5 bg-gray-400 rounded-full animate-pulse delay-300" />
      </div>
    </div>
  </div>
);

const ChatMessage = ({ message }) => {
  const isBotMessage = message.sender === 'bot';
  
  return (
    <div className={`flex items-start space-x-2 ${
      !isBotMessage ? 'flex-row-reverse space-x-reverse' : 'flex-row'
    }`}>
      {isBotMessage && (
        <div className="w-8 h-8 rounded-full flex items-center justify-center bg-blue-600 flex-shrink-0 
          shadow-lg shadow-blue-500/20">
          <Bot size={18} className="text-white" />
        </div>
      )}
      
      <div className={`group py-2.5 px-4 rounded-lg max-w-[80%] text-sm leading-relaxed 
        ${isBotMessage 
          ? 'bg-gray-800 text-white shadow-lg shadow-gray-950/10' 
          : 'bg-blue-600 text-white shadow-lg shadow-blue-500/20'}`}
      >
        {isBotMessage ? (
          <div 
            className="prose prose-invert max-w-none prose-p:leading-normal prose-pre:mt-2 prose-pre:mb-2"
            dangerouslySetInnerHTML={{
              __html: DOMPurify.sanitize(marked.parse(message.text))
            }} 
          />
        ) : (
          <div className="flex items-center gap-2">
            <span>{message.text}</span>
          </div>
        )}
      </div>
    </div>
  );
};

const SuggestionButton = ({ text, onClick }) => (
  <button
    onClick={onClick}
    className="px-3 py-1.5 bg-gray-800 text-white text-sm rounded-lg 
      hover:bg-gray-700 transition-all duration-200
      border border-gray-700 flex items-center gap-2 group
      hover:shadow-lg hover:shadow-blue-500/10"
  >
    <span>{text}</span>
    <SendIcon className="text-gray-400 group-hover:text-white transition-colors" />
  </button>
);

const ChatWindow = ({ isOpen, onClose }) => {
  const [messages, setMessages] = useState([{
    id: 1,
    text: "Hello, I'm the virtual assistant for Md. Golam Mostofa(shuvo). How can I assist you today?",
    sender: "bot",
    reactions: { likes: 0, dislikes: 0 },
    copied: false
  }]);
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [sessionId] = useState('default');
  const [showSuggestions, setShowSuggestions] = useState(true);
  const messagesEndRef = React.useRef(null);

  const queryAPI = useCallback(async (message) => {
    const MAX_RETRIES = 3;
    const BASE_DELAY = 1000;

    for (let attempt = 0; attempt <= MAX_RETRIES; attempt++) {
      try {
        const chat = await createPortfolioAssistant();
        const response = await chat(message, sessionId);
        return response;
      } catch (error) {
        if (attempt === MAX_RETRIES) {
          if (error.name === 'AbortError') {
            return "Request timeout. Please try again.";
          }
          if (error.message?.includes('Failed to fetch')) {
            return "Connection error. Please check your internet connection.";
          }
          return "An unexpected error occurred. Please try again later.";
        }
        
        const jitter = Math.random() * 1000;
        await new Promise(resolve => 
          setTimeout(resolve, BASE_DELAY * Math.pow(2, attempt) + jitter)
        );
      }
    }
  }, [sessionId]);

  const handleQuery = async (query) => {
    setShowSuggestions(false);
    const userMessage = {
      id: Date.now(),
      text: query,
      sender: "user",
      reactions: { likes: 0, dislikes: 0 },
      copied: false
    };
    
    setMessages(prev => [...prev, userMessage]);
    setIsTyping(true);

    try {
      const response = await queryAPI(query);
      const botMessage = {
        id: Date.now() + 1,
        text: response || "I apologize, but I couldn't process your message. Please try again.",
        sender: "bot",
        reactions: { likes: 0, dislikes: 0 },
        copied: false
      };
      setMessages(prev => [...prev, botMessage]);
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage = {
        id: Date.now() + 1,
        text: "I apologize, but an error occurred. Please try again.",
        sender: "bot",
        reactions: { likes: 0, dislikes: 0 },
        copied: false
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
    }
  };

  const handleSendMessage = useCallback(async () => {
    const trimmedMessage = newMessage.trim();
    if (!trimmedMessage) return;
    
    setShowSuggestions(false);
    handleQuery(trimmedMessage);
    setNewMessage('');
  }, [newMessage]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: markdownStyles }} />
      <div 
        className={`fixed z-50 
          md:w-96 w-full 
          md:h-[36rem] h-[36rem]
          md:bottom-24 md:right-6 
          bottom-0.5 right-0
          bg-gray-900 shadow-2xl 
          rounded-lg
          transition-all duration-300 ease-in-out
          border border-gray-800/50
          backdrop-blur-xl
          flex flex-col
          ${isOpen ? 'translate-y-0 opacity-100' : 'translate-y-full md:translate-y-0 md:scale-95 opacity-0'}`}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="bg-gray-900/95 p-4 rounded-t-lg flex items-center justify-between border-b border-gray-800/50
          backdrop-blur-xl">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center
              shadow-lg shadow-blue-500/20">
              <Bot size={18} className="text-white" />
            </div>
            <h3 className="text-white font-medium text-base">Portfolio Assistant</h3>
          </div>
          <div className="flex items-center gap-2">
            <button 
              onClick={(e) => {
                e.stopPropagation();
                onClose();
              }}
              className="text-gray-400 hover:text-white transition-colors p-1 rounded-lg
                hover:bg-gray-800/50"
              aria-label="Close chat"
            >
              <X size={20} />
            </button>
          </div>
        </div>

        {/* Messages Area */}
        <div className="flex-1 p-4 overflow-y-auto bg-gray-900/95 space-y-4 backdrop-blur-xl">
          {messages.map((msg) => (
            <ChatMessage key={msg.id} message={msg} />
          ))}
          
          {isTyping && <TypingIndicator />}
          <div ref={messagesEndRef} />
        </div>

        {/* Input Area */}
        <div className="p-4 border-t border-gray-800/50 bg-gray-900/95 backdrop-blur-xl">
          {showSuggestions && (
            <div className="flex items-center gap-2 mb-3 overflow-x-auto pb-2 scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-transparent">
              <SuggestionButton 
                text="Tell me about him"
                onClick={(e) => {
                  e.stopPropagation();
                  handleQuery("Tell me about him");
                }}
              />
              <SuggestionButton 
                text="Shuvo's work experience"
                onClick={(e) => {
                  e.stopPropagation();
                  handleQuery("Shuvo's work experience");
                }}
              />
            </div>
          )}
          <div className="flex items-center gap-2" onClick={(e) => e.stopPropagation()}>
            <textarea
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage();
                }
              }}
              placeholder="Type your message..."
              className="flex-1 min-h-[40px] max-h-[80px] py-2 px-3
                bg-gray-800 text-white border border-gray-700 rounded-lg resize-none
                focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500
                placeholder:text-gray-400 text-sm leading-normal
                transition-all duration-200
                shadow-lg shadow-gray-950/10"
              style={{ overflowY: 'auto', lineHeight: '1.5' }}
            />
            <button
              onClick={handleSendMessage}
              disabled={!newMessage.trim()}
              className="h-10 w-10 flex items-center justify-center 
                bg-blue-600 rounded-lg
                hover:bg-blue-700 transition-all duration-200
                disabled:opacity-50 disabled:cursor-not-allowed
                flex-shrink-0
                shadow-lg shadow-blue-500/20"
            >
              <SendHorizonal size={18} className="text-white" />
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default ChatWindow;