import { FaGithub, FaLinkedinIn, FaMedium, FaEnvelope } from 'react-icons/fa';
import { SiKaggle, SiLeetcode, SiGooglescholar } from 'react-icons/si';

import {
    food_festival,
    bizzntek,
    litellm,
    Python_7,
    dsa,
    drc_2024,
    duet,
    gpi,
    mem0,
    chromadb,
    postges,
    mobile,
    serenus,
    django,
    tensorflow,
    pytorch,
    github,
    langcahin,
    dj_rest,
    bs,
    sisBd,
    leetcode,
    devolved_ai,
    ai_assistant,
    data_scientist,
    artificial_intelligence,
    backend,
    creator,
    web,
    javascript,
    typescript,
    html,
    css,
    reactjs,
    redux,
    tailwind,
    nodejs,
    mongodb,
    git,
    figma,
    docker,
    meta,
    starbucks,
    tesla,
    shopify,
    carrent,
    sara,
    astra,
    amg,
    kaggle,
    ai_chatbot,
    face_mask_door,
    ag_ai_doc,
    emusic,
    sk_best_q,
    uomp,
    athena,
    momtaz,
    obidur,
    kazary,
  } from "../assets";
import { color } from 'framer-motion';
  
  export const navLinks = [
    {
      id: "about",
      title: "About",
    },
    {
      id: "works",
      title: "Works",
    },

    {
      id: "educations",
      title: "Educations",
    },

    {
      id: "projects",
      title: "Projects",
    },

    {
      id: "publications",
      title: "Publications",
    },
    {
      id: "photos",
      title: "Photos",
    },

    {
      id: "bootcamps",
      title: "Bootcamps",
    },

    {
      id: "contact",
      title: "Contact",
    },
  ];
  
  const services = [
    {
      title: "AI Model Developer",
      icon: artificial_intelligence,
    },
    {
      title: "LLM Engineer",
      icon: ai_assistant,
    },
    {
      title: "Data Scientists",
      icon: data_scientist,
    },
    {
      title: "Backend Developer",
      icon: backend,
    },
    
  ];
  
  const technologies = [

    {
      name: "LangChain",
      icon: langcahin,
    },
    {
      name: "LiteLLM",
      icon: litellm,
    },

    {
      name: "Mem0",
      icon: mem0,
    },
    
    {
      name: "Pytorch",
      icon: pytorch,
    },
    
    {
      name: "Tensorflow",
      icon: tensorflow,
    },
    
    
    {
      name: "Django",
      icon: django,
    },
    {
      name: "Django REST",
      icon: dj_rest,
    },
    {
      name: "JavaScript",
      icon: javascript,
    },
    {
      name: "HTML 5",
      icon: html,
    },
    {
      name: "CSS 3",
      icon: css,
    },
    {
      name: "React JS",
      icon: reactjs,
    },
    
    {
      name: "Tailwind CSS",
      icon: tailwind,
    },
    
    {
      name: "MongoDB",
      icon: mongodb,
    },
    {
      name: "PostgreSQL",
      icon: postges,
    },

    {
      name: "ChromaDB",
      icon: chromadb,
    },
    
    {
      name: "git",
      icon: git,
    },
    {
      name: "GitHub",
      icon: github,
    },
    {
      name: "docker",
      icon: docker,
    },
  ];
  
  const experiences = [
    {
      title: "Lead AI Engineer",
      company_name: "Bizzntek Ltd, Dhaka, BD",
      icon: bizzntek,
      iconBg: "#FFFF",
      date: "Apr 2025 - Present",
      points: [
        "Lead and mentor a team of 3 engineers,overseeing project planning,task delegation,and technical guidance.",
        "Architect and implement end‑to‑end automation solutions using Microsoft Power Automate to reduce manual processes and improveworkflowefficiency.",
        "Design and deployconversational Copilot agents through Microsoft Copilot Studio to assist users and automate routine inquiries.",
        "Collaborate cross‑functionally with business stakeholders to understand process requirements and deliver customized automationstrategies.",
        "Ensurequality, security, and performance of deployed solutions through regular testing, review, and optimization.",
        "Coordinate documentation and knowledge sharing within the team to maintain best practices and improve productivity."
      ],
    },
    {
      title: "Machine Learning Engineer",
      company_name: "Devolved AI, LA, USA",
      icon: devolved_ai,
      iconBg: "#383E56",
      date: "Sep 2023 - Mar 2025",
      points: [
        "Large data sets were pre‑processeded for LLMs using cleaning, feature engineering, normalization, tokenization, and text encoding techniques.",
        "Creating evaluation and instruction data sets to support the ongoing development and validation of models.",
        "Improve task‑specific results and overall accuracy by pre‑training, optimizing, and fine‑tuning models to meet performance criteria.",
        "Evaluate the Performance of Large Language Models(LLMs).",
        "Deploy Large Language Models (LLMs)",
      ],
    },

    {
      title: "LLM Engineer",
      company_name: "Serenus One, LA, USA",
      icon: serenus,
      iconBg: "#FFFF",
      date: "Jun 2023 - Aug 2023",
      points: [
        "Fine-tune and optimize models for performance and accuracy.",
        "Improvement Chatbot performance.",
        "RAG system implementation",
        "API endpoint making",
        "Collaborate with software engineers to integrate AI models into production systems.",
      ],
    },

    {
      title: "Notebooks Expert",
      company_name: "Kaggle, Online Judge",
      icon: kaggle,
      iconBg: "#E6DEDD",
      date: "Aug 2022 - Present",
      points: [
        "I enhanced my data science, Machine Learning, and NLP skills through Kaggle competitions.",
        'I am top 5% (44/955) on ”Improve a Fixed Model the Data‑Centric Way!”  competition.',
      ],
    },
    {
      title: "Problem Solver",
      company_name: "LeetCode, Online Judge",
      icon: leetcode,
      iconBg: "#383E56",
      date: "Aug 2022 - Present",
      points: [
        "I have solved over 330 problems on LeetCode.",
        "Maximum Contest Rating: 1433"
      ],
    },
    // {
    //   title: "Intern IoT Engineer",
    //   company_name: "SiS Inflexionpoint Ltd, Dhaka, BD",
    //   icon: sisBd,
    //   iconBg: "#E6DEDD",
    //   date: "Jun 2023 - Aug 2023",
    //   points: [
    //     "Developing and maintaining The Cow Collar System"
    //   ],
    // },
    // {
    //   title: "Intern Software Engineer",
    //   company_name: "Brain Station 23, Dhaka, BD",
    //   icon: bs,
    //   iconBg: "#fff",
    //   date: "One Month",
    //   points: [
    //     "Git and GitHub.",
    //     "Academic Internship",
    //   ],
    // },
  ];
  
  const testimonials = [
    {
      testimonial:
        "He is an exceptional student whose dedication, creativity, and leadership consistently inspire excellence in everything he do.",
      name: "Prof. Dr. MD. Obaidur Rahman",
      designation: "Ex-Head of CSE Department",
      company: "DUET(<EMAIL>)",
      image: obidur,
    },
    {
      testimonial:
        "He has consistently demonstrated exceptional dedication, leadership, and a proactive approach, making him an invaluable member of any team.",
      name: "Dr. Momotaz Begum",
      designation: "Professor, CSE Department",
      company: "DUET(<EMAIL>)",
      image: momtaz,
    },
    {
      testimonial:
        "As an advisor, I have been impressed by he unwavering dedication, intellectual curiosity, and ability to excel in both academic and professional pursuits.",
      name: "Sumaya Kazary",
      designation: "Assistant Professor, CSE Department",
      company: "DUET(<EMAIL>)",
      image: kazary,
    },
  ];
  
  const projects = [
    {
      name: "Athena-2",
      description:
        "Athena is an advanced large language model fine-tuned from the Llama3.1 8b LLM, optimized for high performance and specialized use cases.",
      tags: [
        {
          name: "Fine Tuning",
          color: "blue-text-gradient",
        },
        {
          name: "Federated Learning",
          color: "green-text-gradient",
        },
        // {
        //   name: "tailwind",
        //   color: "pink-text-gradient",
        // },
      ],
      image: athena,
      source_code_link: "https://athena.devolvedai.com/",
    },
    {
      name: "Sara's AI Assistant",
      description:
        "This is an LLM-based AI assistant that can perform mental health. It is fintuned from ChatGPT 4o-mini, Llama3.2, and Claude",
      tags: [
        {
          name: "Fine Tuning",
          color: "blue-text-gradient",
        },
        {
          name: "LiteLLM",
          color: "green-text-gradient",
        },
        {
          name: "Langchain",
          color: "pink-text-gradient",
        },
        {
          name: "FastAPI",
          color: "blue-text-gradient",
        },
      ],
      image: sara,
      source_code_link: "https://guide.serenus.one/",
    },
    {
      name: "Astra LLM",
      description:
        "The Astra LLM is a base model itself. It have 1.4b parameter.",
      tags: [
        {
          name: "Build LLM from Scratch",
          color: "blue-text-gradient",
        },
        {
          name: "PyTorch",
          color: "green-text-gradient",
        },
        {
          name: "LLM Architecture",
          color: "pink-text-gradient",
        },
      ],
      image: astra,
      source_code_link: "https://www.kaggle.com/models/golammostofas/astra/",
    },

    {
      name: "Automatic MCQ Generator",
      description:
        "Automitacally generate MCQ from a given text.",
      tags: [
        {
          name: "LangChain",
          color: "blue-text-gradient",
        },
        {
          name: "Gemini API",
          color: "green-text-gradient",
        },
        // {
        //   name: "tailwind",
        //   color: "pink-text-gradient",
        // },
      ],
      image: amg,
      source_code_link: "https://github.com/shuvo881/Automatic-MCQ-Generate",
    },

    {
      name: "AI Chat Bot",
      description:
        "This is an AI chatbot that can chat with you. which is fine-tuned from the llama3.2 8b instruction LLM.",
      tags: [
        {
          name: "Django",
          color: "blue-text-gradient",
        },
        {
          name: "REST-API",
          color: "green-text-gradient",
        },
        {
          name: "React JS",
          color: "pink-text-gradient",
        },
      ],
      image: ai_chatbot,
      source_code_link: "https://github.com/shuvo881/AI_Chat_Bot",
    },
    {
      name: "Agricultural AI Doctor",
      description:
        "This is a computer vision-based AI model that can detect plant diseases and suggest remedies by the leaf.",
      tags: [
        {
          name: "Machine Learning",
          color: "blue-text-gradient",
        },
        {
          name: "Django",
          color: "green-text-gradient",
        },
        {
          name: "Bootstrap CSS",
          color: "pink-text-gradient",
        },
      ],
      image: ag_ai_doc,
      source_code_link: "https://github.com/shuvo881/Agricultural_AI_Doctor"
    },
    {
      name: "Face Mask Detect Door System",
      description:
        "It is a security‑based AI door System, that detects whether a person wearing a mask or not. if yes with a mask, and open it. Otherwise, close it.",
      tags: [
        {
          name: "Keras",
          color: "blue-text-gradient",
        },
        {
          name: "Open-cv",
          color: "green-text-gradient",
        },
        {
          name: "Supervised Machine Learning",
          color: "pink-text-gradient",
        },
      ],
      image: face_mask_door,
      source_code_link: "https://github.com/shuvo881/Face-Mask-Detect-Door-System"
    },

    

    {
      name: "Emotion Based Music System",
      description:
        "Play a music according to your face emotion.",
      tags: [
        {
          name: "DeepFace",
          color: "blue-text-gradient",
        },
        {
          name: "Open-cv",
          color: "green-text-gradient",
        },
      ],
      image: emusic,
      source_code_link: "https://github.com/shuvo881/Emusic"
    },

    {
      name: "SK Best Quality ",
      description:
        "This is e‑commerce base website, where has add product, add to card, update card, order method, etc.",
      tags: [
        {
          name: "Django",
          color: "blue-text-gradient",
        },
        {
          name: "REST-API",
          color: "green-text-gradient",
        },
        {
          name: "Docker",
          color: "Supervised Machine Learning",
        },
        {
          name: "EC2",
          color: "green-text-gradient",
        },
        {
          name: "JavaScript",
          color: "blue-text-gradient",
        },
        {
          name: "Tailwind CSS",
          color: "pink-text-gradient",
        },
      ],
      image: sk_best_q,
      source_code_link: "http://************:8000/"
    },

    {
      name: "UOMP",
      description:
        "It is an online marketplace website, where you may sell products or purchase products.",
      tags: [
        {
          name: "Django",
          color: "blue-text-gradient",
        },
        {
          name: "Postgre-SQL",
          color: "green-text-gradient",
        },
        {
          name: "Docker",
          color: "Supervised Machine Learning",
        },
        {
          name: "EC2",
          color: "green-text-gradient",
        },
        {
          name: "JavaScript",
          color: "blue-text-gradient",
        },
        {
          name: "Tailwind CSS",
          color: "pink-text-gradient",
        },
      ],
      image: uomp,
      source_code_link: "https://github.com/shuvo881/Urgent_Online_Marketplace"
    },

  ];

  const publications = [
    {
      id: 1,
      title: "M-DBSCAN: Modified DBSCAN Clustering Algorithm for Detecting and Controlling Outliers",
      venue: "SAC '24: 39th ACM/SIGAPP Symposium on Applied Computing (Avila, Spain)",
      year: 2024,
      authors: ['Momotaz Begum', 'Mehedi Hasan Shuvo', 'Md. Golam Mostofa', 'Abm Kamrul Islam Riad', 'Md Arabin Islam Talukder', 'Mst Shapna Akter', 'Hossain Shahriar'],
      impact: "Featured in Top ML Publications",
      citation: `@inproceedings{10.1145/3605098.3636188,
          author = {Begum, Momotaz and Shuvo, Mehedi Hasan and Mostofa, Md. Golam and Riad, Abm Kamrul Islam and Talukder, Md Arabin Islam and Akter, Mst Shapna and Shahriar, Hossain},
          title = {M-DBSCAN: Modified DBSCAN Clustering Algorithm for Detecting and Controlling Outliers},
          year = {2024},
          isbn = {9798400702433},
          publisher = {Association for Computing Machinery},
          address = {New York, NY, USA},
          url = {https://doi.org/10.1145/3605098.3636188},
          doi = {10.1145/3605098.3636188},
          abstract = {Outlier reduction is crucial in computer science for improving data quality, analysis accuracy, and modeling robustness. Selection and modification of DBSCAN parameters are essential for optimal clustering accuracy and outlier detection. We developed an adaptive technique to minimize outliers in the DBSCAN algorithm using a linear congruential method (LCM) to determine values of Epsilon (Eps) and Min-Points (MinPts), known as modified DBSCAN (M-DBSCAN). To enhance the DBSCAN method, we create integer random numbers for MinPts (1--100) and floating numbers for Eps (0.1--1.5) using LCM. We adjusted parameter lists to reduce outliers based on MinPts and Eps values. We choose parameters based on dataset features and requirements, balancing clustering sensitivity and noise treatment. For experiment result analysis we use the Silhouette Score (SS) method. M-DBSCAN improved all cases and it has 50\% poorer outlier accuracy than DBSCAN.},
          booktitle = {Proceedings of the 39th ACM/SIGAPP Symposium on Applied Computing},
          pages = {1034–1035},
          numpages = {2},
          keywords = {data clustering, outlier handling, DBSCAN, M-DBSCAN},
          location = {Avila, Spain},
          series = {SAC '24}
          }`,
      citationCount: 0,
      link: "https://dl.acm.org/doi/10.1145/3605098.3636188",
      description: "Outlier reduction is crucial in computer science for improving data quality, analysis accuracy, and modeling robustness. Selection and modification of DBSCAN parameters are essential for optimal clustering accuracy and outlier detection. We developed an adaptive technique to minimize outliers in the DBSCAN algorithm using a linear congruential method (LCM) to determine values of Epsilon (Eps) and Min-Points (MinPts), known as modified DBSCAN (M-DBSCAN). To enhance the DBSCAN method, we create integer random numbers for MinPts (1--100) and floating numbers for Eps (0.1--1.5) using LCM. We adjusted parameter lists to reduce outliers based on MinPts and Eps values. We choose parameters based on dataset features and requirements, balancing clustering sensitivity and noise treatment. For experiment result analysis we use the Silhouette Score (SS) method. M-DBSCAN improved all cases and it has 50% poorer outlier accuracy than DBSCAN.",
      tags: [
        {
          name: "DBSCAN",
          color: "text-blue-500",
        },
        {
          name: "Unsupervised Learning",
          color: "text-green-500",
        }
      ]
    },
  
  ];

  const education = [
    {
      school: "Dhaka University of Engineering and Technology - DUET",
      degree: "BSc in Computer Science and Engineering",
      duration: "2018 - 2023",
      description: "Specialized in Artificial Intelligence and Machine Learning with a focus on Deep Learning and Natural Language Processing. Conducted research on transformer architectures and their applications in federated learning environments.",
      achievements: [
        "Published research paper on M-DBSCAN",
        "Received DRC FEST - 2022 Award"
      ],
      courses: [
        "Deep Learning",
        "Natural Language Processing",
        "Computer Vision",
        "Distributed Systems",
        "Machine Learning",
        "AI Ethics"
      ],
      image: duet
    },
    {
      school: "Gopalganj Polytechnic Institute",
      degree: "Diploma in Computer Technology",
      duration: "2014 - 2018",
      description: "Completed undergraduate studies with a focus on algorithms and theoretical computer science. Participated in undergraduate research program working on distributed systems and parallel computing.",
      achievements: [
        "Undergraduate basic computer science",
      ],
      courses: [
        "Algorithms",
        "Data Structures",
        "Operating Systems",
        "Computer Networks",
        "Database Systems",
        "Software Engineering"
      ],
      image: gpi
    }
  ];
  
  const photos = [
    {
      title: "Food Festival",
      description: 'Food Festival organized by Bizzntek Ltd.',
      image: food_festival, // Replace with actual image path
      tags: ["Food", "Bizzntek"],
    },
    {
      title: "with Teammate",
      description: 'Machine learning team of Devolved AI in Bangladesh quarter',
      image: dsa, // Replace with actual image path
      tags: ["Discussion",],
    },

    {
      title: "DRC FEST - 2k22",
      description: 'We became the "2nd Runner Up" for our innovative(Face Mask Door System) in the Project Showcase segments of "DRC FEST - 2k22" that is organized by DUET Robotics Club .',
      image: drc_2024, // Replace with actual image path
      tags: ["Robotics", "ML", "Computer Vison"],
    },
    
    

    // {
    //   title: "Image With Teammet",
    //   description: '',
    //   image: dsa, // Replace with actual image path
    //   tags: ["Robotics", "ML", "Computer Vison"],
    // },

  ];

  const bootcamps = [
    {
      title: "Structural and Object-Oriented Programming in Python",
      description: "Mastering Python: A Comprehensive Guide to Structural and Object-Oriented Programming.",
      duration: "7 days",
      startDate: "Novbemer 10, 2024",
      participants: 50,
      topics: ["Data Types", "Control Flow", "Functions", "Generators and Decorators", "OOP"],
      image: Python_7,
      // videoUrl: "https://youtu.be/MnSQBY-_9Mo?si=qyh7jO547V0XHnW3",
      recordedSessions: [
        {
          title: "Session 1-7: Introduction to Python",
          url: "https://www.youtube.com/watch?v=CtsqNynniUQ&list=PL9JsgurS7NW5z_OxyHvDS9vl6h-oAJ8j8&index=6"
        },
        // {
        //   title: "Session 2: Basics Operators, Control Flow",
        //   url: "https://youtu.be/lWRWme-lec4?si=JuJgKTSrQqo3dpG6"
        // },
        // {
        //   title: "Session 3: Function and Generator, Decorator",
        //   url: "https://youtu.be/4k4Nd_VvEBo?si=NYZY15uVGunImEHF"
        // },
        // {
        //   title: "Session 5: Class Object, Encapsulation",
        //   url: "https://youtu.be/32uILfuZA-8?si=_pYLSE814eaIR_vo"
        // },
        // {
        //   title: "Session 6: Abstraction, Inheritance",
        //   url: "https://youtu.be/DYsZrDYYcPM?si=PaeowsdVonxeTn81"
        // },
        // {
        //   title: "Session 7: Polymorphism",
        //   url: "https://youtu.be/MnSQBY-_9Mo?si=i3yQe9htWpt1O1kM"
        // }
      ]
    },
    // Add more bootcamp entries as needed
  ];

  const socialLinks = [
    {
      href: "https://www.linkedin.com/in/mdgolammostofa705/",
      icon: FaLinkedinIn,
      name: "LinkedIn",
      color: '#333',
    },
    {
      href: "https://github.com/shuvo881",
      icon: FaGithub,
      name: "GitHub",
     color: '#0077b5'

    },
    {
      href: "https://scholar.google.com/citations?user=mxW1qOoAAAAJ",
      icon: SiGooglescholar,
      name: "Google Scholar",
      color: '#00ab6c'

    },
    {
      href: "https://www.kaggle.com/golammostofas",
      icon: SiKaggle,
      name: "Kaggle",
      color: '#1da1f2',

    },
    {
      href: "https://leetcode.com/u/golammostofa10001/",
      icon: SiLeetcode,
      name: "LeetCode",
      color: '#44B78B'

    },
    {
      href: "https://medium.com/@golammostofa10001",
      icon: FaMedium,
      name: "Medium",
      color: '#000'      
    },
    {
        name: 'Email',
        href: 'mailto:<EMAIL>',
        icon: FaEnvelope,
        color: '#ea4335'
    },

  ];

  export {socialLinks, bootcamps, photos, services, education, technologies, experiences, testimonials, projects, publications };