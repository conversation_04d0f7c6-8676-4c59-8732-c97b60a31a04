import React from 'react';
import { AlertTriangle, Home, ArrowLeft } from 'lucide-react';

const RouterError = () => {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-sm p-6 space-y-4">
        <div className="flex items-center justify-center">
          <AlertTriangle className="text-yellow-500 w-12 h-12" />
        </div>
        
        <div className="text-center space-y-2">
          <h2 className="text-lg font-semibold text-gray-900">Route Not Found</h2>
          <div className="bg-gray-100 rounded p-2 font-mono text-sm">
            No routes matched location "/photosx"
          </div>
        </div>

        <div className="flex justify-center gap-4 pt-4">
          <button 
            onClick={() => window.history.back()}
            className="flex items-center gap-2 px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
          >
            <ArrowLeft className="w-4 h-4" />
            Back
          </button>
          
          <a 
            href="/"
            className="flex items-center gap-2 px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700"
          >
            <Home className="w-4 h-4" />
            Home
          </a>
        </div>
      </div>
    </div>
  );
};

export default RouterError;