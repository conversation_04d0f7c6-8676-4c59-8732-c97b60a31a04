import { Tilt } from 'react-tilt';
import React from 'react';
import { motion } from 'framer-motion';
import { styles } from '../styles';
import { services } from '../constants';
import { fadeIn, textVariant } from '../utils/motion';
import { SectionWrapper } from '../hoc';

// ServiceCard component
const ServiceCard = ({ index, title, icon }) => (
  <Tilt className='xs:w-[250px] w-full'>
    <motion.div
      variants={fadeIn("right", "spring", 0.5 * index, 0.75)}
      className='w-full green-pink-gradient p-[1px] rounded-[20px] shadow-card'
    >
      <div
        className='bg-tertiary rounded-[20px] py-5 px-12 min-h-[280px] flex justify-evenly items-center flex-col'
      >
        <img src={icon} alt={title} className='w-16 h-16 object-contain rounded-xl' />
        <h3 className='text-white text-[20px] font-bold text-center'>{title}</h3>
      </div>
    </motion.div>
  </Tilt>
);

// About component
const About = () => {
  return (
    <>
      <motion.div variants={textVariant()}>
        <p className={styles.sectionSubText}>Introduction</p>
        <h2 className={styles.sectionHeadText}>Overview.</h2>
      </motion.div>

      
      <motion.p
        variants={fadeIn("", "", 0.1, 1)}
        className='mt-4 text-secondary text-[17px] max-w-3xl leading-[30px]'
      >
        Hi, I'm <strong>Md. Golam Mostofa</strong>, an experienced AI Engineer and Team Leader with <strong>2+ years</strong> of expertise in building production-ready Large Language Models, AI agents, and scalable backend systems. Currently leading a team of 3 engineers while architecting end-to-end automation solutions using Microsoft Power Platform. I have a proven track record in LLM fine-tuning, RAG implementation, and deploying AI-powered applications that drive measurable business impact.
        
      </motion.p>

      <motion.p
        variants={fadeIn("", "", 0.1, 1)}
        className='mt-4 text-secondary text-[17px] max-w-3xl leading-[30px]'
      >
        At <strong>Devolved AI</strong>, I specialize in fine-tuning <strong>Large Language Models (LLMs)</strong> and deploying high-performance applications using <strong>Django</strong>, <strong>REST APIs</strong>, and <strong>Docker</strong>.
      </motion.p>

      <motion.p
        variants={fadeIn("", "", 0.1, 1)}
        className='mt-4 text-secondary text-[17px] max-w-3xl leading-[30px]'
      >
        I have deep experience with technologies like <strong>PyTorch</strong>, <strong>TensorFlow</strong>, and <strong>NLP</strong>, along with strong backend deployment skills using <strong>AWS EC2</strong> and <strong>PostgreSQL</strong>. My key projects include <strong>Athena LLM</strong> and an <strong>AI Chat Bot</strong>, where I combined advanced AI models with robust backend infrastructures.
      </motion.p>

      <motion.p
        variants={fadeIn("", "", 0.1, 1)}
        className='mt-4 text-secondary text-[17px] max-w-3xl leading-[30px]'
      >
        I hold a <strong>B.Sc. in Computer Science and Engineering</strong> from <strong>DUET</strong>, with published research in clustering algorithms. You may view my published paper <a href="https://dl.acm.org/doi/10.1145/3605098.3636188" className="text-blue-500 underline" target="_blank" rel="noopener noreferrer"><strong>here</strong></a>.
      </motion.p>

      <motion.p
        variants={fadeIn("", "", 0.1, 1)}
        className='mt-4 text-secondary text-[17px] max-w-3xl leading-[30px]'
      >
        I continuously refine my skills through <a href="https://leetcode.com/u/golammostofa10001/" className="text-blue-500 underline" target="_blank" rel="noopener noreferrer"><strong>LeetCode</strong></a>, <a href="https://www.kaggle.com/golammostofas" className="text-blue-500 underline" target="_blank" rel="noopener noreferrer"><strong>Kaggle</strong></a>, and <a href="https://scholar.google.com/citations?user=mxW1qOoAAAAJ" className="text-blue-500 underline" target="_blank" rel="noopener noreferrer"><strong>Google Scholar</strong></a>, solving real-world machine learning and backend challenges.
      </motion.p>

      <motion.p
        variants={fadeIn("", "", 0.1, 1)}
        className='mt-4 text-secondary text-[17px] max-w-3xl leading-[30px]'
      >
        Feel free to connect with me at <a href="mailto:<EMAIL>" className="text-blue-500 underline"><strong><EMAIL></strong></a> or on <a href="https://www.linkedin.com/in/mdgolammostofa705/" className="text-blue-500 underline" target="_blank" rel="noopener noreferrer"><strong>LinkedIn</strong></a>.
      </motion.p>

      <div className='mt-20 flex flex-wrap gap-10'>
        {services.map((service, index) => (
          <ServiceCard key={service.title} index={index} {...service} />
        ))}
      </div>
    </>
  );
};

export default SectionWrapper(About, "about");
